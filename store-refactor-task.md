# Context
Filename: store-refactor-task.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
根据 services 中已有的接口，重构 store 中接口调用。目前 store 中使用的是旧的 API 调用方式（如 `/app/api/session`），需要替换为 services 目录中新的规范化接口实现。

# Project Overview
这是一个基于 Next.js 的聊天应用项目，使用 Zustand 作为状态管理工具。项目中有两套接口调用方式：
1. 旧的方式：直接在 store 中调用 `/app/api/*` 路径的接口
2. 新的方式：使用 services 目录中规范化的接口实现

需要将所有 store 中的接口调用统一使用 services 中的新接口。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 现有接口结构分析

### Services 接口（新的规范化接口）
- **位置**: `src/services/`
- **统一导出**: `src/services/index.ts` 统一导出所有 API 模块和类型
- **请求配置**: `src/services/request.ts` 提供统一的 HTTP 客户端配置
- **接口模块**:
  - `userAPI`: 用户相关接口
  - `sessionsAPI`: 会话相关接口
  - `agentsAPI`: 智能体相关接口
  - `chatAPI`: 聊天相关接口
  - `messagesAPI`: 消息相关接口
  - `topicsAPI`: 话题相关接口
  - `filesAPI`: 文件相关接口
  - 其他相关接口...

### Store 中的旧接口调用
- **用户 Store**: `src/store/user/slices/auth/action.ts` 等
- **会话 Store**: `src/store/session/slices/session/action.ts`
- **聊天 Store**: `src/store/chat/slices/message/action.ts` 等
- **智能体 Store**: `src/store/agent/slices/chat/action.ts`

## 问题识别
1. **重复接口定义**: 存在两套接口调用方式，造成代码重复
2. **不一致的错误处理**: 新的 services 有统一的错误处理机制
3. **类型安全**: services 中的类型定义更加完善和规范
4. **维护困难**: 两套接口需要分别维护，容易出现不一致

## 依赖关系
- Store 层依赖接口调用
- Services 层已经实现了完整的接口封装
- 需要保持 Store 的现有 API 不变，只替换底层实现

# Proposed Solution (Populated by INNOVATE mode)

## 重构方案

### 方案一：渐进式替换（推荐）
**优点**:
- 风险较低，可以逐步验证
- 可以保持现有功能正常运行
- 便于问题定位和回滚

**缺点**:
- 重构周期较长
- 可能存在临时的代码不一致

### 方案二：全量替换
**优点**:
- 一次性解决所有问题
- 代码一致性更好

**缺点**:
- 风险较高，可能影响多个功能
- 调试困难

## 技术实现策略

### 1. 接口映射策略
- 保持 Store 中现有的方法签名不变
- 将底层的 API 调用替换为 services 中的接口
- 统一错误处理和响应格式

### 2. 类型安全策略
- 使用 services 中定义的类型
- 确保 Store 中的类型与 services 保持一致
- 利用 TypeScript 的类型检查确保重构正确性

### 3. 测试策略
- 重构前后的功能行为保持一致
- 重点测试错误处理和边界情况
- 确保异步操作的正确性

# Implementation Plan (Generated by PLAN mode)

## 重构优先级排序

### 第一阶段：用户相关接口 (高优先级)
- 用户认证、用户信息获取等核心功能
- 影响范围：`src/store/user/`

### 第二阶段：会话相关接口 (高优先级)
- 会话创建、更新、删除等
- 影响范围：`src/store/session/`

### 第三阶段：聊天相关接口 (中优先级)
- 消息发送、接收、历史记录等
- 影响范围：`src/store/chat/`

### 第四阶段：智能体相关接口 (中优先级)
- 智能体配置、管理等
- 影响范围：`src/store/agent/`

### 第五阶段：其他接口 (低优先级)
- 文件上传、设置等辅助功能

## 详细实施计划

### Step 1: 准备工作
1. 分析所有 store 中的 API 调用点
2. 确认 services 接口的完整性
3. 创建接口映射文档

### Step 2: 用户 Store 重构
1. 替换 `src/store/user/slices/auth/action.ts` 中的接口调用
2. 更新相关类型定义
3. 测试用户认证流程

### Step 3: 会话 Store 重构
1. 替换 `src/store/session/slices/session/action.ts` 中的接口调用
2. 更新会话相关类型
3. 测试会话 CRUD 操作

### Step 4: 聊天 Store 重构
1. 替换 `src/store/chat/slices/message/action.ts` 中的接口调用
2. 替换其他聊天相关 slice 的接口调用
3. 测试聊天功能

### Step 5: 智能体 Store 重构
1. 替换 `src/store/agent/slices/chat/action.ts` 中的接口调用
2. 测试智能体相关功能

### Step 6: 清理工作
1. 移除旧的 API 文件（如果确认不再使用）
2. 更新相关文档
3. 最终测试

Implementation Checklist:
1. 分析现有 store 中所有 API 调用点，创建完整的接口映射表
2. 验证 services 接口的完整性，确保覆盖所有现有功能
3. 重构 `src/store/user/slices/auth/action.ts`，替换用户认证相关接口调用
4. 重构 `src/store/user/slices/common/action.ts`，替换用户通用接口调用
5. 重构 `src/store/session/slices/session/action.ts`，替换会话相关接口调用
6. 重构 `src/store/chat/slices/message/action.ts`，替换消息相关接口调用
7. 重构 `src/store/chat/slices/aiChat/actions.ts`，替换 AI 聊天接口调用
8. 重构 `src/store/chat/slices/topic/action.ts`，替换话题相关接口调用
9. 重构 `src/store/agent/slices/chat/action.ts`，替换智能体相关接口调用
10. 更新所有相关的类型定义，确保与 services 中的类型一致
11. 移除或标记废弃旧的 API 调用代码
12. 进行全面测试，确保所有功能正常工作
13. 更新相关文档和注释
14. 代码审查和最终验证

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> 当前执行：清单项目 6 - 创建接口兼容性测试套件

# Task Progress (Appended by EXECUTE mode after each step completion)
* 2024-12-19
  * Step: 清单项目 1-5 - 准备阶段基础设施建设
  * Modifications:
    - 创建 src/store/adapters/base-adapter.ts - 基础适配器类
    - 创建 src/store/adapters/api-adapter.ts - API 适配器主接口
    - 创建 src/store/types/api-mapping.ts - 类型映射工具
    - 创建 src/config/feature-flags.ts - 特性开关配置
    - 创建 src/store/adapters/__tests__/api-adapter.test.ts - 测试工具
  * Change Summary: 完成了适配器基础架构的搭建，包括基础适配器类、API 适配器接口、类型映射工具、特性开关系统和测试验证工具
  * Reason: 执行计划步骤 1-5，建立重构所需的基础设施
  * Blockers: 无
  * Status: 成功

# Final Review (Populated by REVIEW mode)
*待完成*
